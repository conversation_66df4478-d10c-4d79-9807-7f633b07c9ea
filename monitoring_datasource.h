#ifndef MONITORING_DATASOURCE_H
#define MONITORING_DATASOURCE_H

#include <QObject>
#include <QTimer>
#include <QVariantList>
#include <QDateTime>
#include <QThread>
#include <QMutex>
#include <QThreadPool>
#include <QVector>
#include <QStringList>
#include <QVariantMap>
#include <QDebug>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

// Qt 5.x forward declaration
namespace QtCharts {
    class QAbstractSeries;
}

class MonitoringDataSource : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QVariantList smokeO2Data READ smokeO2Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeCOData READ smokeCOData NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeSwitch1Data READ smokeSwitch1Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeTableData READ smokeTableData NOTIFY smokeTableDataChanged)
    Q_PROPERTY(QStringList boilerList READ boilerList NOTIFY boilerListChanged)
    Q_PROPERTY(QString currentBoiler READ currentBoiler WRITE setCurrentBoiler NOTIFY currentBoilerChanged)
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)
    Q_PROPERTY(bool isDataConnected READ isDataConnected NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString currentTemperature READ currentTemperature NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentVoltage READ currentVoltage NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentCurrent READ currentCurrent NOTIFY currentDataChanged)
    // Q_PROPERTY(bool isRedisConnected read isRedisConnected NOTIFY redisConnectionChanged) // 已删除Redis

public:
    explicit MonitoringDataSource(QObject *parent = nullptr);

    // 属性访问器 - 从内存循环缓冲区读取数据
    QVariantList smokeO2Data() const;
    QVariantList smokeCOData() const;
    QVariantList smokeSwitch1Data() const;
    QVariantList smokeTableData() const { return m_smokeTableData; }
    QStringList boilerList() const { return m_boilerList; }
    QString currentBoiler() const { return m_currentBoiler; }
    bool isRunning() const { return m_isRunning; }
    bool isDataConnected() const { return m_isDataConnected; }
    QString connectionStatus() const { return m_connectionStatus; }
    QString currentTemperature() const { return m_currentTemperature; }
    QString currentVoltage() const { return m_currentVoltage; }
    QString currentCurrent() const { return m_currentCurrent; }

    // 获取当前设备的采集间隔（秒）
    Q_INVOKABLE int getCurrentCollectionInterval() const;

    // 获取指定时间范围窗口需要的最大数据点数
    Q_INVOKABLE int getMaxWindowDataPoints(int timeRangeMinutes = 30) const;

    // 获取指定时间范围的数据
    Q_INVOKABLE QVariantList getSmokeO2Data(int timeRangeMinutes = 30) const;
    Q_INVOKABLE QVariantList getSmokeCOData(int timeRangeMinutes = 30) const;
    Q_INVOKABLE QVariantList getSmokeSwitch1Data(int timeRangeMinutes = 30) const;

    void setIsRunning(bool running);
    void setCurrentBoiler(const QString &boiler);

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearData();
    // 图表更新方法 - 支持多时间范围视图
    void updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int timeRangeMinutes = 30);
    void updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int timeRangeMinutes = 30);

signals:
    void smokeDataChanged();
    void smokeTableDataChanged();
    void boilerListChanged();
    void currentBoilerChanged();
    void isRunningChanged();
    void chartDataUpdated();
    void dataConnectionChanged();
    void currentDataChanged();

private slots:
    void updateData();

private:
    void updateSmokeData();
    void loadBoilerList();
    void reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler);
    void addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1);
    void updateTimerInterval();

    // 内存数据管理方法
    void addDataPoint(double o2, double co, double temperature, double voltage, double current, int switch1);
    void clearMemoryData();

    // 多时间缓冲区管理方法
    void addDataPointToBuffer(TimeRangeBuffer& buffer, double o2, double co, double temperature, double voltage, double current, int switch1);
    QVariantList getDataFromBuffer(const TimeRangeBuffer& buffer, const QString& dataType, int requestedMinutes) const;
    TimeRangeBuffer& getBufferForTimeRange(int timeRangeMinutes);

    QTimer *m_timer;

    // 循环缓冲区数据结构 - 纯数据存储，不包含时间信息
    struct DataPoint {
        double o2;
        double co;
        double temperature;
        double voltage;
        double current;
        int switch1;
        // 时间信息由前端根据数据点索引和采样间隔计算
        // 采样间隔：3秒/点，由前端使用 index * 3000ms 计算时间
    };

    // 定义不同时间范围的缓冲区大小
    static const int BUFFER_60MIN = 1200;   // 60分钟，3秒间隔
    static const int BUFFER_8HOUR = 9600;   // 8小时，3秒间隔
    static const int BUFFER_12HOUR = 14400; // 12小时，3秒间隔
    static const int BUFFER_24HOUR = 28800; // 24小时，3秒间隔

    // 多时间缓冲区结构
    struct TimeRangeBuffer {
        QVector<DataPoint> dataBuffer;
        int currentIndex;  // 当前写入位置
        int dataCount;     // 实际数据点数
        int maxSize;       // 缓冲区最大大小

        TimeRangeBuffer(int size) : dataBuffer(size), currentIndex(0), dataCount(0), maxSize(size) {}
    };

    // 四个不同时间范围的缓冲区
    TimeRangeBuffer m_buffer60Min;
    TimeRangeBuffer m_buffer8Hour;
    TimeRangeBuffer m_buffer12Hour;
    TimeRangeBuffer m_buffer24Hour;

    // 兼容性：保留原有的30分钟缓冲区（实际使用60分钟缓冲区的前半部分）
    static const int MAX_DATA_POINTS = 600; // 30分钟按照3秒采集的频率那就是600个点。

    // 相对时间轴相关变量
    QDateTime m_dataStartTime;  // 数据采集开始时间
    bool m_dataStartTimeSet;    // 是否已设置开始时间

    // 表格数据
    QVariantList m_smokeTableData;

    // 表格更新计数器 - 每三次采集更新一次表格
    int m_tableUpdateCounter;

    // 烟气分析仪相关
    QStringList m_boilerList;  // 保持变量名以兼容现有代码
    QString m_currentBoiler;   // 保持变量名以兼容现有代码

    // 反吹反馈控制相关
    bool m_isBackflowActive;           // 反吹反馈是否激活
    bool m_isDataUpdateSuspended;      // 氧气和一氧化碳数据更新是否暂停
    QTimer *m_backflowDelayTimer;      // 反吹反馈延迟恢复定时器
    int m_backflowDelayTime;           // 延迟时间（秒）
    QString m_suspendedO2Value;        // 暂停时保存的氧气浓度值
    QString m_suspendedCOValue;        // 暂停时保存的一氧化碳浓度值

    // 反吹反馈控制方法
    void checkBackflowStatus(int switch1);
    void suspendO2COUpdates();
    void resumeO2COUpdates();
    int getBackflowDelayTime() const;

    bool m_isRunning;
    bool m_isDataConnected;
    QString m_connectionStatus;

    // 当前数据值
    QString m_currentTemperature;
    QString m_currentVoltage;
    QString m_currentCurrent;

    static const int MAX_TABLE_ROWS = 10;
    static const int TABLE_UPDATE_INTERVAL = 4;  // 每4次采集更新一次表格
};

#endif // MONITORING_DATASOURCE_H
